import { DashboardSidebar } from "@/components/dashboard/DashboardSidebar";
import { UserProfileHeader } from "@/components/dashboard/UserProfileHeader";
import { AuthDebugger } from "@/components/ui/auth-debugger";
import { Toaster } from "@/components/ui/toaster";
import { useState } from "react";
import { Outlet } from "react-router-dom";

export function DashboardLayout() {
  const [sidebarOpen, setSidebarOpen] = useState(true);

  // LoadingStateManager handles all loading logic at the app level
  // No need for additional loading checks here

  return (
    <div className="flex h-full w-full bg-background">
      {/* Sidebar */}
      <DashboardSidebar open={sidebarOpen} setOpen={setSidebarOpen} />

      {/* Main content */}
      <div className="flex flex-col flex-1 w-full h-full">
        {/* Top navigation */}
        <UserProfileHeader toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />

        {/* Main content area */}
        <main className="flex-1 p-6 overflow-auto">
          <Outlet />
        </main>
      </div>

      {/* Global UI elements */}
      <Toaster />
      <AuthDebugger />
    </div>
  );
}
